import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:material_symbols_icons/symbols.dart';

import '../controllers/unified_homework_controller.dart';
import '../models/homework_filter_config.dart';

/// Enhanced filter bar widget for assignment type selection with unified loading
class UnifiedAssignmentTypeFilterBar extends ConsumerWidget {
  const UnifiedAssignmentTypeFilterBar({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = Theme.of(context);
    final selectedFilter = ref.watch(unifiedHomeworkFilterProvider);
    final filterOptionsAsync = ref.watch(unifiedFilterOptionsProvider);

    return Container(
      height: 48.h,
      margin: EdgeInsets.symmetric(horizontal: 16.w),
      child: filterOptionsAsync.when(
        loading: () => _LoadingFilterBar(theme: theme),
        error: (error, stackTrace) => _ErrorFilterBar(
          error: error.toString(),
          onRetry: () => ref.invalidate(unifiedFilterOptionsProvider),
          theme: theme,
        ),
        data: (filterCounts) => _FilterChips(
          selectedFilter: selectedFilter,
          filterCounts: filterCounts,
          theme: theme,
          onFilterSelected: (filter) {
            ref.read(unifiedHomeworkFilterProvider.notifier).setFilter(filter);
          },
        ),
      ),
    );
  }
}

/// Loading state for filter bar
class _LoadingFilterBar extends StatelessWidget {
  final ThemeData theme;

  const _LoadingFilterBar({required this.theme});

  @override
  Widget build(BuildContext context) {
    final isDark = theme.brightness == Brightness.dark;

    return Row(
      children: [
        // Show skeleton chips while loading
        for (int i = 0; i < 4; i++) ...[
          Container(
            height: 32.h,
            width: 80.w,
            margin: EdgeInsets.only(right: 8.w),
            decoration: BoxDecoration(
              color: isDark
                  ? theme.colorScheme.surfaceVariant.withOpacity(0.3)
                  : theme.colorScheme.surfaceVariant.withOpacity(0.5),
              borderRadius: BorderRadius.circular(16.r),
            ),
          ),
        ],
      ],
    );
  }
}

/// Error state for filter bar
class _ErrorFilterBar extends StatelessWidget {
  final String error;
  final VoidCallback onRetry;
  final ThemeData theme;

  const _ErrorFilterBar({
    required this.error,
    required this.onRetry,
    required this.theme,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        Icon(Symbols.error, size: 20.sp, color: theme.colorScheme.error),
        SizedBox(width: 8.w),
        Expanded(
          child: Text(
            'Failed to load filters',
            style: TextStyle(fontSize: 14.sp, color: theme.colorScheme.error),
          ),
        ),
        TextButton(
          onPressed: onRetry,
          child: Text(
            'Retry',
            style: TextStyle(fontSize: 14.sp, color: theme.colorScheme.primary),
          ),
        ),
      ],
    );
  }
}

/// Filter chips widget
class _FilterChips extends StatelessWidget {
  final UnifiedHomeworkFilterType selectedFilter;
  final Map<UnifiedHomeworkFilterType, int> filterCounts;
  final ThemeData theme;
  final Function(UnifiedHomeworkFilterType) onFilterSelected;

  const _FilterChips({
    required this.selectedFilter,
    required this.filterCounts,
    required this.theme,
    required this.onFilterSelected,
  });

  @override
  Widget build(BuildContext context) {
    return ListView.separated(
      scrollDirection: Axis.horizontal,
      itemCount: UnifiedHomeworkFilterType.values.length,
      separatorBuilder: (context, index) => SizedBox(width: 8.w),
      itemBuilder: (context, index) {
        final filterType = UnifiedHomeworkFilterType.values[index];
        final count = filterCounts[filterType] ?? 0;
        final isSelected = selectedFilter == filterType;

        return _FilterChip(
          filterType: filterType,
          count: count,
          isSelected: isSelected,
          theme: theme,
          onTap: () => onFilterSelected(filterType),
        );
      },
    );
  }
}

/// Individual filter chip widget
class _FilterChip extends StatelessWidget {
  final UnifiedHomeworkFilterType filterType;
  final int count;
  final bool isSelected;
  final ThemeData theme;
  final VoidCallback onTap;

  const _FilterChip({
    required this.filterType,
    required this.count,
    required this.isSelected,
    required this.theme,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final isDark = theme.brightness == Brightness.dark;

    final backgroundColor = isSelected
        ? theme.colorScheme.primary
        : isDark
        ? theme.colorScheme.surfaceVariant
        : theme.colorScheme.surfaceVariant.withOpacity(0.7);

    final textColor = isSelected
        ? theme.colorScheme.onPrimary
        : isDark
        ? theme.colorScheme.onSurfaceVariant
        : theme.colorScheme.onSurfaceVariant;

    final borderColor = isSelected
        ? theme.colorScheme.primary
        : isDark
        ? theme.colorScheme.outline.withOpacity(0.5)
        : theme.colorScheme.outline.withOpacity(0.3);

    return GestureDetector(
      onTap: onTap,
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 200),
        padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
        decoration: BoxDecoration(
          color: backgroundColor,
          borderRadius: BorderRadius.circular(16.r),
          border: Border.all(color: borderColor, width: 1),
          boxShadow: isSelected
              ? [
                  BoxShadow(
                    color: theme.colorScheme.primary.withOpacity(0.3),
                    blurRadius: 8,
                    offset: const Offset(0, 2),
                  ),
                ]
              : null,
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              filterType.shortLabel,
              style: TextStyle(
                fontSize: 14.sp,
                fontWeight: isSelected ? FontWeight.w600 : FontWeight.w500,
                color: textColor,
              ),
            ),
            if (count > 0) ...[
              SizedBox(width: 6.w),
              Container(
                padding: EdgeInsets.symmetric(horizontal: 6.w, vertical: 2.h),
                decoration: BoxDecoration(
                  color: isSelected
                      ? theme.colorScheme.onPrimary.withOpacity(0.2)
                      : theme.colorScheme.primary.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(10.r),
                ),
                child: Text(
                  count.toString(),
                  style: TextStyle(
                    fontSize: 12.sp,
                    fontWeight: FontWeight.w600,
                    color: isSelected
                        ? theme.colorScheme.onPrimary
                        : theme.colorScheme.primary,
                  ),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }
}

/// Helper widget for showing filter descriptions (optional)
class UnifiedFilterDescription extends ConsumerWidget {
  const UnifiedFilterDescription({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final selectedFilter = ref.watch(unifiedHomeworkFilterProvider);
    final theme = Theme.of(context);

    String description;
    switch (selectedFilter) {
      case UnifiedHomeworkFilterType.all:
        description = 'Showing all your homework assignments';
        break;
      case UnifiedHomeworkFilterType.classAssignments:
        description = 'Showing assignments given to your entire class';
        break;
      case UnifiedHomeworkFilterType.individual:
        description = 'Showing tasks assigned specifically to you';
        break;
      case UnifiedHomeworkFilterType.group:
        description = 'Showing group projects you\'re part of';
        break;
    }

    return Container(
      margin: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
      child: Text(
        description,
        style: theme.textTheme.bodySmall?.copyWith(
          color: theme.colorScheme.onSurfaceVariant,
          fontSize: 12.sp,
        ),
      ),
    );
  }
}
