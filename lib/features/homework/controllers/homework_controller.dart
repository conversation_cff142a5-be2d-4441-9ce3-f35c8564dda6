import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:logger/logger.dart';

import '../../../core/enums/homework/assignment_type.dart';
import '../../../core/providers/auth_providers.dart';
import '../models/homework_model.dart';
import '../models/homework_submission_model.dart';
import '../repositories/homework_repository.dart';

/// Logger for homework controller
final _logger = Logger();

/// Provider for the homework repository instance
final homeworkRepositoryProvider = Provider<HomeworkRepository>((ref) {
  return HomeworkRepository();
});

/// Provider for current user ID from authentication
final currentUserIdProvider = Provider<String>((ref) {
  final authState = ref.watch(authStateProvider);
  return authState.when(
    data: (state) => state.user?.id ?? 'anonymous',
    loading: () => 'anonymous',
    error: (_, __) => 'anonymous',
  );
});

/// Provider to fetch homework list for a specific date
final homeworkListProvider =
    FutureProvider.family<List<HomeworkModel>, DateTime>((ref, date) async {
      final repository = ref.read(homeworkRepositoryProvider);

      try {
        _logger.i('Fetching homework list for date: ${date.toIso8601String()}');
        final homeworkList = await repository.getHomeworkForDate(date);
        _logger.i('Successfully fetched ${homeworkList.length} homework items');
        return homeworkList;
      } catch (e) {
        _logger.e('Error fetching homework list: $e');
        rethrow;
      }
    });

/// Provider to fetch all homework (for "All" filter)
final allHomeworkProvider = FutureProvider<List<HomeworkModel>>((ref) async {
  final repository = ref.read(homeworkRepositoryProvider);

  try {
    _logger.i('Fetching all homework');
    final homeworkList = await repository.getAllHomework();
    _logger.i(
      'Successfully fetched ${homeworkList.length} total homework items',
    );
    return homeworkList;
  } catch (e) {
    _logger.e('Error fetching all homework: $e');
    rethrow;
  }
});

/// Provider to fetch homework for a date range
final homeworkDateRangeProvider =
    FutureProvider.family<
      List<HomeworkModel>,
      ({DateTime startDate, DateTime endDate})
    >((ref, params) async {
      final repository = ref.read(homeworkRepositoryProvider);

      try {
        _logger.i(
          'Fetching homework for date range: ${params.startDate.toIso8601String()} to ${params.endDate.toIso8601String()}',
        );
        final homeworkList = await repository.getHomeworkForDateRange(
          params.startDate,
          params.endDate,
        );
        _logger.i(
          'Successfully fetched ${homeworkList.length} homework items for date range',
        );
        return homeworkList;
      } catch (e) {
        _logger.e('Error fetching homework for date range: $e');
        rethrow;
      }
    });

/// Provider to fetch homework details by ID
final homeworkDetailProvider = FutureProvider.family<HomeworkModel?, String>((
  ref,
  homeworkId,
) async {
  final repository = ref.read(homeworkRepositoryProvider);

  try {
    _logger.i('Fetching homework detail for ID: $homeworkId');
    final homework = await repository.getHomeworkById(homeworkId);
    if (homework != null) {
      _logger.i('Successfully fetched homework detail: ${homework.title}');
    } else {
      _logger.w('Homework not found with ID: $homeworkId');
    }
    return homework;
  } catch (e) {
    _logger.e('Error fetching homework detail: $e');
    rethrow;
  }
});

/// Provider to fetch submission for a specific homework and user
final submissionProvider =
    FutureProvider.family<
      HomeworkSubmissionModel?,
      ({String homeworkId, String userId})
    >((ref, params) async {
      final repository = ref.read(homeworkRepositoryProvider);

      try {
        _logger.i(
          'Fetching submission for homework: ${params.homeworkId}, user: ${params.userId}',
        );
        final submission = await repository.getSubmission(
          params.homeworkId,
          params.userId,
        );
        if (submission != null) {
          _logger.i('Successfully fetched submission');
        } else {
          _logger.i('No submission found');
        }
        return submission;
      } catch (e) {
        _logger.e('Error fetching submission: $e');
        rethrow;
      }
    });

/// Provider to fetch submission for current user
final currentUserSubmissionProvider =
    FutureProvider.family<HomeworkSubmissionModel?, String>((
      ref,
      homeworkId,
    ) async {
      final userId = ref.read(currentUserIdProvider);
      final submission = await ref.read(
        submissionProvider((homeworkId: homeworkId, userId: userId)).future,
      );
      return submission;
    });

/// Provider to fetch homework status (done/undone) for current user
final homeworkStatusProvider = FutureProvider.family<bool, String>((
  ref,
  homeworkId,
) async {
  final repository = ref.read(homeworkRepositoryProvider);
  final userId = ref.read(currentUserIdProvider);

  try {
    _logger.i(
      'Fetching homework status for homework: $homeworkId, user: $userId',
    );
    final isDone = await repository.getHomeworkStatus(homeworkId, userId);
    _logger.i('Homework status: $isDone');
    return isDone;
  } catch (e) {
    _logger.e('Error fetching homework status: $e');
    rethrow;
  }
});

/// Provider to submit homework
final submitHomeworkProvider =
    FutureProvider.family<void, HomeworkSubmissionModel>((
      ref,
      submission,
    ) async {
      final repository = ref.read(homeworkRepositoryProvider);

      try {
        _logger.i('Submitting homework: ${submission.homeworkId}');
        await repository.submitHomework(submission);
        _logger.i('Successfully submitted homework');

        // Invalidate related providers to refresh UI
        ref.invalidate(submissionProvider);
        ref.invalidate(currentUserSubmissionProvider);
        ref.invalidate(homeworkListProvider);
        ref.invalidate(allHomeworkProvider);
        ref.invalidate(homeworkDateRangeProvider);

        // Invalidate unified providers
        ref.invalidate(userHomeworkProvider);
        // Note: Import unified providers when available
        // ref.invalidate(unifiedHomeworkProvider);
        // ref.invalidate(unifiedFilteredHomeworkProvider);
      } catch (e) {
        _logger.e('Error submitting homework: $e');
        rethrow;
      }
    });

/// Provider to mark homework as done/undone
final markHomeworkDoneProvider =
    FutureProvider.family<
      void,
      ({String homeworkId, String userId, bool isDone})
    >((ref, params) async {
      final repository = ref.read(homeworkRepositoryProvider);

      try {
        _logger.i(
          'Marking homework ${params.isDone ? 'done' : 'undone'}: ${params.homeworkId}',
        );
        await repository.markHomeworkDone(
          params.homeworkId,
          params.userId,
          params.isDone,
        );
        _logger.i(
          'Successfully marked homework ${params.isDone ? 'done' : 'undone'}',
        );

        // Invalidate related providers to refresh UI
        ref.invalidate(homeworkStatusProvider);
        ref.invalidate(homeworkListProvider);
        ref.invalidate(allHomeworkProvider);
        ref.invalidate(homeworkDateRangeProvider);
      } catch (e) {
        _logger.e('Error marking homework done: $e');
        rethrow;
      }
    });

/// Provider to mark homework as done/undone for current user
final markCurrentUserHomeworkDoneProvider =
    FutureProvider.family<void, ({String homeworkId, bool isDone})>((
      ref,
      params,
    ) async {
      final userId = ref.read(currentUserIdProvider);

      return ref.read(
        markHomeworkDoneProvider((
          homeworkId: params.homeworkId,
          userId: userId,
          isDone: params.isDone,
        )).future,
      );
    });

/// Helper provider to get homework with submission status for current user
final homeworkWithSubmissionProvider =
    FutureProvider.family<
      ({HomeworkModel? homework, HomeworkSubmissionModel? submission}),
      String
    >((ref, homeworkId) async {
      final homeworkFuture = ref.read(
        homeworkDetailProvider(homeworkId).future,
      );
      final submissionFuture = ref.read(
        currentUserSubmissionProvider(homeworkId).future,
      );

      final results = await Future.wait([homeworkFuture, submissionFuture]);

      return (
        homework: results[0] as HomeworkModel?,
        submission: results[1] as HomeworkSubmissionModel?,
      );
    });

/// Helper provider to get homework with status for current user
final homeworkWithStatusProvider =
    FutureProvider.family<({HomeworkModel? homework, bool isDone}), String>((
      ref,
      homeworkId,
    ) async {
      final homeworkFuture = ref.read(
        homeworkDetailProvider(homeworkId).future,
      );
      final statusFuture = ref.read(homeworkStatusProvider(homeworkId).future);

      final results = await Future.wait([homeworkFuture, statusFuture]);

      return (
        homework: results[0] as HomeworkModel?,
        isDone: results[1] as bool,
      );
    });

/// Utility class for homework controller operations
class HomeworkControllerUtils {
  /// Invalidate all homework-related providers
  static void invalidateAllHomeworkProviders(WidgetRef ref) {
    ref.invalidate(homeworkListProvider);
    ref.invalidate(allHomeworkProvider);
    ref.invalidate(homeworkDateRangeProvider);
    ref.invalidate(homeworkDetailProvider);
    ref.invalidate(submissionProvider);
    ref.invalidate(currentUserSubmissionProvider);
    ref.invalidate(homeworkStatusProvider);
    ref.invalidate(homeworkWithSubmissionProvider);
    ref.invalidate(homeworkWithStatusProvider);

    // Invalidate new assignment type providers
    ref.invalidate(userHomeworkProvider);
    ref.invalidate(homeworkByAssignmentTypeProvider);
    ref.invalidate(classHomeworkProvider);
    ref.invalidate(individualHomeworkProvider);
    ref.invalidate(groupHomeworkProvider);
  }

  /// Invalidate homework list providers only
  static void invalidateHomeworkListProviders(WidgetRef ref) {
    ref.invalidate(homeworkListProvider);
    ref.invalidate(allHomeworkProvider);
    ref.invalidate(homeworkDateRangeProvider);

    // Invalidate new assignment type list providers
    ref.invalidate(userHomeworkProvider);
    ref.invalidate(homeworkByAssignmentTypeProvider);
    ref.invalidate(classHomeworkProvider);
    ref.invalidate(individualHomeworkProvider);
    ref.invalidate(groupHomeworkProvider);
  }

  /// Invalidate submission-related providers
  static void invalidateSubmissionProviders(WidgetRef ref) {
    ref.invalidate(submissionProvider);
    ref.invalidate(currentUserSubmissionProvider);
    ref.invalidate(homeworkWithSubmissionProvider);
  }

  /// Invalidate status-related providers
  static void invalidateStatusProviders(WidgetRef ref) {
    ref.invalidate(homeworkStatusProvider);
    ref.invalidate(homeworkWithStatusProvider);
  }
}

/// Provider to fetch homework for current user (includes both class and individual assignments)
final userHomeworkProvider = FutureProvider<List<HomeworkModel>>((ref) async {
  final repository = ref.read(homeworkRepositoryProvider);
  final userId = ref.read(currentUserIdProvider);

  try {
    _logger.i('Fetching homework for current user: $userId');
    final homeworkList = await repository.getHomeworkForUser(userId);
    _logger.i(
      'Successfully fetched ${homeworkList.length} homework items for user $userId',
    );
    return homeworkList;
  } catch (e) {
    _logger.e('Error fetching homework for user: $e');
    rethrow;
  }
});

/// Provider to fetch homework by assignment type
final homeworkByAssignmentTypeProvider =
    FutureProvider.family<List<HomeworkModel>, AssignmentType>((
      ref,
      assignmentType,
    ) async {
      final repository = ref.read(homeworkRepositoryProvider);

      try {
        _logger.i(
          'Fetching homework by assignment type: ${assignmentType.name}',
        );
        final homeworkList = await repository.getHomeworkByAssignmentType(
          assignmentType,
        );
        _logger.i(
          'Successfully fetched ${homeworkList.length} homework items for assignment type ${assignmentType.name}',
        );
        return homeworkList;
      } catch (e) {
        _logger.e('Error fetching homework by assignment type: $e');
        rethrow;
      }
    });

/// Provider to fetch homework for a specific class
final classHomeworkProvider = FutureProvider.family<List<HomeworkModel>, String>((
  ref,
  classId,
) async {
  final repository = ref.read(homeworkRepositoryProvider);

  try {
    _logger.i('Fetching homework for class: $classId');
    final homeworkList = await repository.getHomeworkForClass(classId);
    _logger.i(
      'Successfully fetched ${homeworkList.length} homework items for class $classId',
    );
    return homeworkList;
  } catch (e) {
    _logger.e('Error fetching homework for class: $e');
    rethrow;
  }
});

/// Provider to fetch individual assignments for current user
final individualHomeworkProvider = FutureProvider<List<HomeworkModel>>((
  ref,
) async {
  final repository = ref.read(homeworkRepositoryProvider);
  final userId = ref.read(currentUserIdProvider);

  try {
    _logger.i('Fetching individual homework for current user: $userId');
    final homeworkList = await repository.getIndividualHomeworkForUser(userId);
    _logger.i(
      'Successfully fetched ${homeworkList.length} individual homework items for user $userId',
    );
    return homeworkList;
  } catch (e) {
    _logger.e('Error fetching individual homework for user: $e');
    rethrow;
  }
});

/// Provider to fetch group assignments for current user
final groupHomeworkProvider = FutureProvider<List<HomeworkModel>>((ref) async {
  final repository = ref.read(homeworkRepositoryProvider);
  final userId = ref.read(currentUserIdProvider);

  try {
    _logger.i('Fetching group homework for current user: $userId');
    final homeworkList = await repository.getGroupHomeworkForUser(userId);
    _logger.i(
      'Successfully fetched ${homeworkList.length} group homework items for user $userId',
    );
    return homeworkList;
  } catch (e) {
    _logger.e('Error fetching group homework for user: $e');
    rethrow;
  }
});
