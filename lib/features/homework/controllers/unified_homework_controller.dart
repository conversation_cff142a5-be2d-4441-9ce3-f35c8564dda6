import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:logger/logger.dart';
import 'package:scholara_student/features/homework/controllers/homework_controller.dart';

import '../../../core/enums/homework/assignment_type.dart';
import '../../../core/providers/auth_providers.dart';
import '../models/homework_model.dart';
import '../repositories/homework_repository.dart';

/// Logger for unified homework controller
final _logger = Logger();

/// Repository provider
final homeworkRepositoryProvider = Provider<HomeworkRepository>((ref) {
  return HomeworkRepository();
});

/// Unified homework provider that fetches all homework types for the current user
/// This provides a single loading experience and serves as the primary data source
final unifiedHomeworkProvider = FutureProvider<List<HomeworkModel>>((ref) async {
  final repository = ref.read(homeworkRepositoryProvider);
  final userId = ref.read(currentUserIdProvider);

  return AsyncValue.guard(() async {
    try {
      _logger.i('Fetching unified homework for user: $userId');
      final homeworkList = await repository.getHomeworkForUser(userId);
      _logger.i(
        'Successfully fetched ${homeworkList.length} homework items for user $userId',
      );
      return homeworkList;
    } catch (e) {
      _logger.e('Error fetching unified homework: $e');
      rethrow;
    }
  });
});

/// Unified homework provider for a specific date
/// Uses the same unified loading approach but filters by date
final unifiedHomeworkByDateProvider = 
    FutureProvider.family<List<HomeworkModel>, DateTime>((ref, date) async {
  final repository = ref.read(homeworkRepositoryProvider);
  final userId = ref.read(currentUserIdProvider);

  return AsyncValue.guard(() async {
    try {
      _logger.i(
        'Fetching unified homework for user: $userId on date: ${date.toIso8601String()}',
      );
      final homeworkList = await repository.getHomeworkForUserByDate(userId, date);
      _logger.i(
        'Successfully fetched ${homeworkList.length} homework items for user $userId on date ${date.toIso8601String()}',
      );
      return homeworkList;
    } catch (e) {
      _logger.e('Error fetching unified homework by date: $e');
      rethrow;
    }
  });
});

/// Enum for homework filter types with extensible design
enum UnifiedHomeworkFilterType { 
  all, 
  classAssignments, 
  individual, 
  group,
  // Future assignment types can be added here without breaking changes
}

/// Extension for UnifiedHomeworkFilterType with user-friendly labels
extension UnifiedHomeworkFilterTypeExtension on UnifiedHomeworkFilterType {
  String get label {
    switch (this) {
      case UnifiedHomeworkFilterType.all:
        return 'All Homework';
      case UnifiedHomeworkFilterType.classAssignments:
        return 'Class Assignments';
      case UnifiedHomeworkFilterType.individual:
        return 'Individual Tasks';
      case UnifiedHomeworkFilterType.group:
        return 'Group Projects';
    }
  }

  String get shortLabel {
    switch (this) {
      case UnifiedHomeworkFilterType.all:
        return 'All';
      case UnifiedHomeworkFilterType.classAssignments:
        return 'Class';
      case UnifiedHomeworkFilterType.individual:
        return 'Individual';
      case UnifiedHomeworkFilterType.group:
        return 'Group';
    }
  }

  /// Maps filter type to assignment type for filtering
  AssignmentType? get assignmentType {
    switch (this) {
      case UnifiedHomeworkFilterType.all:
        return null; // No filtering
      case UnifiedHomeworkFilterType.classAssignments:
        return AssignmentType.classAssignment;
      case UnifiedHomeworkFilterType.individual:
        return AssignmentType.individual;
      case UnifiedHomeworkFilterType.group:
        return AssignmentType.group;
    }
  }
}

/// State notifier for unified homework filter
class UnifiedHomeworkFilterNotifier extends StateNotifier<UnifiedHomeworkFilterType> {
  UnifiedHomeworkFilterNotifier() : super(UnifiedHomeworkFilterType.all);

  void setFilter(UnifiedHomeworkFilterType filterType) {
    _logger.i('Setting unified homework filter to: ${filterType.label}');
    state = filterType;
  }

  void reset() {
    _logger.i('Resetting unified homework filter to All');
    state = UnifiedHomeworkFilterType.all;
  }
}

/// Provider for unified homework filter state
final unifiedHomeworkFilterProvider = 
    StateNotifierProvider<UnifiedHomeworkFilterNotifier, UnifiedHomeworkFilterType>((ref) {
  return UnifiedHomeworkFilterNotifier();
});

/// Provider for class filter (used with class assignments)
final classFilterProvider = StateProvider<String?>((ref) => null);

/// Unified filtered homework provider that applies filters to the unified data source
/// This ensures single loading experience regardless of filters applied
final unifiedFilteredHomeworkProvider = FutureProvider<List<HomeworkModel>>((ref) async {
  final filterType = ref.watch(unifiedHomeworkFilterProvider);
  final selectedClassId = ref.watch(classFilterProvider);

  return AsyncValue.guard(() async {
    try {
      _logger.i(
        'Applying unified homework filter: ${filterType.label}, class: $selectedClassId',
      );

      // Get all user homework from the unified provider
      final allUserHomework = await ref.read(unifiedHomeworkProvider.future);

      List<HomeworkModel> filteredHomework;

      // Apply assignment type filter
      if (filterType.assignmentType != null) {
        filteredHomework = allUserHomework
            .where((hw) => hw.assignmentType == filterType.assignmentType)
            .toList();
      } else {
        filteredHomework = List.from(allUserHomework);
      }

      // Apply class filter if selected and relevant
      if (selectedClassId != null &&
          filterType == UnifiedHomeworkFilterType.classAssignments) {
        filteredHomework = filteredHomework
            .where((homework) => homework.classId == selectedClassId)
            .toList();
      }

      _logger.i(
        'Successfully filtered homework: ${filteredHomework.length} items',
      );
      return filteredHomework;
    } catch (e) {
      _logger.e('Error filtering unified homework: $e');
      rethrow;
    }
  });
});

/// Unified filtered homework provider by date
final unifiedFilteredHomeworkByDateProvider = 
    FutureProvider.family<List<HomeworkModel>, DateTime>((ref, date) async {
  final filterType = ref.watch(unifiedHomeworkFilterProvider);
  final selectedClassId = ref.watch(classFilterProvider);

  return AsyncValue.guard(() async {
    try {
      _logger.i(
        'Applying unified homework filter for date ${date.toIso8601String()}: ${filterType.label}, class: $selectedClassId',
      );

      // Get homework for the specific date from the unified provider
      final allHomeworkForDate = await ref.read(
        unifiedHomeworkByDateProvider(date).future,
      );

      List<HomeworkModel> filteredHomework;

      // Apply assignment type filter
      if (filterType.assignmentType != null) {
        filteredHomework = allHomeworkForDate
            .where((hw) => hw.assignmentType == filterType.assignmentType)
            .toList();
      } else {
        filteredHomework = List.from(allHomeworkForDate);
      }

      // Apply class filter if selected and relevant
      if (selectedClassId != null &&
          filterType == UnifiedHomeworkFilterType.classAssignments) {
        filteredHomework = filteredHomework
            .where((homework) => homework.classId == selectedClassId)
            .toList();
      }

      _logger.i(
        'Successfully filtered homework by date: ${filteredHomework.length} items',
      );
      return filteredHomework;
    } catch (e) {
      _logger.e('Error filtering unified homework by date: $e');
      rethrow;
    }
  });
});

/// Provider to get available filter options with counts
final unifiedFilterOptionsProvider = FutureProvider<Map<UnifiedHomeworkFilterType, int>>((
  ref,
) async {
  return AsyncValue.guard(() async {
    try {
      // Use unified homework loading for consistent experience
      final allHomework = await ref.read(unifiedHomeworkProvider.future);

      final counts = <UnifiedHomeworkFilterType, int>{
        UnifiedHomeworkFilterType.all: allHomework.length,
        UnifiedHomeworkFilterType.classAssignments: allHomework
            .where((hw) => hw.assignmentType == AssignmentType.classAssignment)
            .length,
        UnifiedHomeworkFilterType.individual: allHomework
            .where((hw) => hw.assignmentType == AssignmentType.individual)
            .length,
        UnifiedHomeworkFilterType.group: allHomework
            .where((hw) => hw.assignmentType == AssignmentType.group)
            .length,
      };

      _logger.i('Successfully calculated filter counts: $counts');
      return counts;
    } catch (e) {
      _logger.e('Error calculating filter counts: $e');
      rethrow;
    }
  });
});
