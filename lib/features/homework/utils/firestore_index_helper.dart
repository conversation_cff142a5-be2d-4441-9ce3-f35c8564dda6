import 'package:flutter/foundation.dart';

/// Helper utility for Firestore index management and troubleshooting
class FirestoreIndexHelper {
  /// Display required indexes for the homework system
  static void displayRequiredIndexes() {
    debugPrint('=== REQUIRED FIRESTORE INDEXES ===');
    debugPrint('');
    debugPrint('1. Homework Collection - Assignment Type with Ordering');
    debugPrint('   Collection: homeworks');
    debugPrint('   Fields:');
    debugPrint('   - assignmentType (Ascending)');
    debugPrint('   - assignedAt (Descending)');
    debugPrint('');
    debugPrint('2. Homework Collection - Class Assignment with Ordering');
    debugPrint('   Collection: homeworks');
    debugPrint('   Fields:');
    debugPrint('   - classId (Ascending)');
    debugPrint('   - assignmentType (Ascending)');
    debugPrint('   - assignedAt (Descending)');
    debugPrint('');
    debugPrint('3. Classes Collection - Student Classes with Ordering');
    debugPrint('   Collection: classes');
    debugPrint('   Fields:');
    debugPrint('   - studentIds (Array Contains)');
    debugPrint('   - isActive (Ascending)');
    debugPrint('   - name (Ascending)');
    debugPrint('');
    debugPrint('4. Classes Collection - Teacher Classes with Ordering');
    debugPrint('   Collection: classes');
    debugPrint('   Fields:');
    debugPrint('   - teacherId (Ascending)');
    debugPrint('   - isActive (Ascending)');
    debugPrint('   - name (Ascending)');
    debugPrint('');
    debugPrint('Create these indexes in Firebase Console:');
    debugPrint(
      'https://console.firebase.google.com/project/scholara-91fca/firestore/indexes',
    );
    debugPrint('=====================================');
  }

  /// Generate firestore.indexes.json content
  static String generateIndexesJson() {
    return '''
{
  "indexes": [
    {
      "collectionGroup": "homeworks",
      "queryScope": "COLLECTION",
      "fields": [
        {
          "fieldPath": "assignmentType",
          "order": "ASCENDING"
        },
        {
          "fieldPath": "assignedAt",
          "order": "DESCENDING"
        }
      ]
    },
    {
      "collectionGroup": "homeworks",
      "queryScope": "COLLECTION",
      "fields": [
        {
          "fieldPath": "classId",
          "order": "ASCENDING"
        },
        {
          "fieldPath": "assignmentType",
          "order": "ASCENDING"
        },
        {
          "fieldPath": "assignedAt",
          "order": "DESCENDING"
        }
      ]
    }
  ],
  "fieldOverrides": []
}
''';
  }

  /// Display troubleshooting information for common index issues
  static void displayTroubleshootingInfo() {
    debugPrint('=== FIRESTORE INDEX TROUBLESHOOTING ===');
    debugPrint('');
    debugPrint('Common Error: "The query requires an index"');
    debugPrint(
      'Solution: Create the missing composite index in Firebase Console',
    );
    debugPrint('');
    debugPrint('Steps to create indexes:');
    debugPrint('1. Go to Firebase Console');
    debugPrint('2. Select project: scholara-91fca');
    debugPrint('3. Navigate to Firestore Database > Indexes');
    debugPrint('4. Click "Create Index"');
    debugPrint('5. Add the required fields as shown above');
    debugPrint('');
    debugPrint('Alternative: Use Firebase CLI');
    debugPrint('1. Create firestore.indexes.json in project root');
    debugPrint('2. Add the JSON content shown above');
    debugPrint('3. Run: firebase deploy --only firestore:indexes');
    debugPrint('');
    debugPrint('Note: Index creation can take several minutes');
    debugPrint('======================================');
  }

  /// Check if we're in development mode and suggest index creation
  static void checkAndSuggestIndexes() {
    if (kDebugMode) {
      debugPrint('🔍 Homework System Index Check');
      debugPrint('If you encounter "query requires an index" errors:');
      debugPrint('Run: FirestoreIndexHelper.displayRequiredIndexes()');
      debugPrint('Or check: firestore_indexes.md for detailed instructions');
    }
  }

  /// Display query optimization tips
  static void displayOptimizationTips() {
    debugPrint('=== QUERY OPTIMIZATION TIPS ===');
    debugPrint('');
    debugPrint('1. Array-Contains Queries:');
    debugPrint('   - Avoid combining with orderBy to prevent complex indexes');
    debugPrint('   - Sort results in memory instead');
    debugPrint('');
    debugPrint('2. Performance:');
    debugPrint('   - Use separate queries for different assignment types');
    debugPrint('   - Combine results in memory');
    debugPrint('   - Leverage Riverpod caching');
    debugPrint('');
    debugPrint('3. Pagination:');
    debugPrint('   - Consider implementing for large datasets');
    debugPrint('   - Use startAfter() for efficient pagination');
    debugPrint('');
    debugPrint('4. Monitoring:');
    debugPrint('   - Check Firebase Console for index status');
    debugPrint('   - Monitor query performance in production');
    debugPrint('===============================');
  }

  /// Generate direct Firebase Console URLs for index creation
  static Map<String, String> getIndexCreationUrls() {
    const baseUrl =
        'https://console.firebase.google.com/v1/r/project/scholara-91fca/firestore/indexes';

    return {
      'assignmentType_assignedAt':
          '$baseUrl?create_composite=ClBwcm9qZWN0cy9zY2hvbGFyYS05MWZjYS9kYXRhYmFzZXMvKGRlZmF1bHQpL2NvbGxlY3Rpb25Hcm91cHMvaG9tZXdvcmtzL2luZGV4ZXMvXxABGhIKDmFzc2lnbm1lbnRUeXBlEAEaDgoKYXNzaWduZWRBdBACGgwKCF9fbmFtZV9fEAI',
      'classId_assignmentType_assignedAt':
          '$baseUrl?create_composite=ClBwcm9qZWN0cy9zY2hvbGFyYS05MWZjYS9kYXRhYmFzZXMvKGRlZmF1bHQpL2NvbGxlY3Rpb25Hcm91cHMvaG9tZXdvcmtzL2luZGV4ZXMvXxABGgkKBWNsYXNzSWQQARoSCg5hc3NpZ25tZW50VHlwZRABGg4KCmFzc2lnbmVkQXQQAhoMCghfX25hbWVfXxAC',
    };
  }

  /// Display direct links for index creation
  static void displayIndexCreationLinks() {
    debugPrint('=== DIRECT INDEX CREATION LINKS ===');
    debugPrint('');
    final urls = getIndexCreationUrls();

    debugPrint('1. Assignment Type + Assigned At Index:');
    debugPrint('   ${urls['assignmentType_assignedAt']}');
    debugPrint('');
    debugPrint('2. Class ID + Assignment Type + Assigned At Index:');
    debugPrint('   ${urls['classId_assignmentType_assignedAt']}');
    debugPrint('');
    debugPrint(
      'Click these links to create indexes directly in Firebase Console',
    );
    debugPrint('==================================');
  }

  /// Run complete index setup guide
  static void runIndexSetupGuide() {
    debugPrint('🚀 FIRESTORE INDEX SETUP GUIDE');
    debugPrint('==============================');

    displayRequiredIndexes();
    debugPrint('');
    displayIndexCreationLinks();
    debugPrint('');
    displayOptimizationTips();
    debugPrint('');
    displayTroubleshootingInfo();

    debugPrint('');
    debugPrint('✅ After creating indexes, restart the app to test queries');
    debugPrint('📝 Save firestore_indexes.md for future reference');
  }
}
