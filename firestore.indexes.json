{"indexes": [{"collectionGroup": "homeworks", "queryScope": "COLLECTION", "fields": [{"fieldPath": "assignmentType", "order": "ASCENDING"}, {"fieldPath": "assignedAt", "order": "DESCENDING"}]}, {"collectionGroup": "homeworks", "queryScope": "COLLECTION", "fields": [{"fieldPath": "classId", "order": "ASCENDING"}, {"fieldPath": "assignmentType", "order": "ASCENDING"}, {"fieldPath": "assignedAt", "order": "DESCENDING"}]}, {"collectionGroup": "classes", "queryScope": "COLLECTION", "fields": [{"fieldPath": "studentIds", "arrayConfig": "CONTAINS"}, {"fieldPath": "isActive", "order": "ASCENDING"}, {"fieldPath": "name", "order": "ASCENDING"}]}, {"collectionGroup": "classes", "queryScope": "COLLECTION", "fields": [{"fieldPath": "teacherId", "order": "ASCENDING"}, {"fieldPath": "isActive", "order": "ASCENDING"}, {"fieldPath": "name", "order": "ASCENDING"}]}], "fieldOverrides": []}